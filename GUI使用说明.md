# 继续教育视频自动学习工具 - GUI版本

## 📋 功能介绍

这是一个带有图形界面的继续教育视频自动学习工具，可以：

- 🎯 自动检测Chrome浏览器中的视频标签页
- 🎬 自动播放视频并监控播放状态
- 📊 实时显示运行日志和进度
- ⚙️ 可自定义视频页面识别URL
- 🖥️ 友好的图形界面操作

## 🚀 使用方法

### 方法1：直接运行Python程序

1. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

2. **运行程序**
   ```bash
   python video_study_gui.py
   ```

### 方法2：使用exe文件（推荐）

1. **打包为exe文件**
   - 双击运行 `build.bat`
   - 或者运行 `python build_exe.py`

2. **运行exe文件**
   - 在 `dist` 目录中找到 `继续教育视频学习工具.exe`
   - 双击运行即可

## 🎮 界面操作

### 主界面说明

1. **视频页面识别URL**
   - 默认值：`https://gp.chinahrt.com/index.html#/v_video?`
   - 可以修改为其他视频平台的URL格式
   - 只有匹配此URL格式的页面才会被识别为视频页面

2. **控制按钮**
   - **开始学习**：启动自动学习程序
   - **停止学习**：停止正在运行的学习程序
   - **清空日志**：清空运行日志显示

3. **运行日志**
   - 显示程序运行的详细信息
   - 包括时间戳、操作步骤、状态信息
   - 支持滚动查看历史日志

4. **状态栏**
   - 显示当前程序状态
   - 如：就绪、正在运行、学习完成等

### 使用步骤

1. **准备工作**
   - 打开Chrome浏览器
   - 在多个标签页中打开继续教育视频课程
   - 确保视频页面URL符合识别格式

2. **启动学习**
   - 在程序中确认或修改"视频页面识别URL"
   - 点击"开始学习"按钮
   - 程序会自动检测标签页并开始学习

3. **监控进度**
   - 在运行日志中查看详细进度
   - 程序会显示每个步骤的执行情况
   - 可以随时点击"停止学习"中断程序

## 📊 程序工作流程

1. **检测Chrome窗口** - 自动找到Chrome浏览器窗口
2. **扫描标签页** - 通过URL去重检测所有标签页
3. **识别视频页面** - 只处理符合URL格式的视频页面
4. **自动播放** - 使用空格键启动视频播放
5. **监控状态** - 每5秒检测一次播放状态
6. **自动切换** - 播放完成后切换到下一个视频页面
7. **完成统计** - 显示学习完成的统计信息

## ⚠️ 注意事项

1. **环境要求**
   - Windows 10/11 系统
   - Google Chrome 浏览器
   - Python 3.7+ （如果运行Python版本）

2. **使用限制**
   - 学习过程中请不要操作电脑
   - 不要最小化或关闭Chrome浏览器
   - 确保电脑不会自动休眠

3. **安全提示**
   - 鼠标移到屏幕左上角可紧急停止程序
   - 程序只会操作Chrome浏览器，不会访问其他程序
   - 所有操作都在本地进行，不会上传任何数据

## 🔧 故障排除

### 常见问题

1. **找不到Chrome窗口**
   - 确保Chrome浏览器已打开
   - 检查窗口标题是否包含"Google Chrome"

2. **无法检测到视频页面**
   - 检查URL是否符合识别格式
   - 确认页面已完全加载

3. **视频无法播放**
   - 手动测试空格键是否能播放视频
   - 检查视频页面是否需要登录

4. **程序运行缓慢**
   - 关闭不必要的程序释放系统资源
   - 检查网络连接是否稳定

### 日志分析

程序会在运行日志中显示详细信息：
- `✓` 表示操作成功
- `❌` 表示操作失败
- `⚠️` 表示警告信息
- `🎯` 表示开始学习视频
- `✅` 表示完成操作

## 📞 技术支持

如果遇到问题，请：
1. 查看运行日志中的错误信息
2. 确认系统环境和依赖是否正确安装
3. 检查Chrome浏览器和视频页面是否正常

## 📝 更新日志

- v1.0.0: 初始版本，支持基本的视频自动学习功能
- v1.1.0: 添加GUI界面，支持自定义URL识别
- v1.2.0: 优化标签页检测，支持打包为exe文件
