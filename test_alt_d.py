#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Alt+D URL获取功能
"""

import time
import pyautogui
import pyperclip

def test_alt_d_url_get():
    """测试Alt+D获取URL的功能"""
    print("测试Alt+D URL获取功能")
    print("请确保Chrome浏览器已打开并在前台")
    print("5秒后开始测试...")
    
    for i in range(5, 0, -1):
        print(f"倒计时: {i}")
        time.sleep(1)
    
    try:
        # 清空剪贴板
        pyperclip.copy("")
        
        # 使用Alt+D选中地址栏
        print("执行Alt+D...")
        pyautogui.hotkey('alt', 'd')
        time.sleep(1)
        
        # 复制URL
        print("复制URL...")
        pyautogui.hotkey('ctrl', 'c')
        time.sleep(1)
        
        # 获取剪贴板内容
        url = pyperclip.paste()
        
        # 按ESC取消选中
        pyautogui.press('escape')
        
        print(f"获取到的URL: {url}")
        print(f"URL长度: {len(url)}")
        
        if url and len(url) > 10:
            print("✓ Alt+D URL获取测试成功！")
        else:
            print("❌ Alt+D URL获取测试失败")
            
    except Exception as e:
        print(f"测试出错: {e}")

if __name__ == "__main__":
    test_alt_d_url_get()
