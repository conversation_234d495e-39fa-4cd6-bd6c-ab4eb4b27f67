@echo off
chcp 65001 >nul
echo ========================================
echo 继续教育视频学习工具 - 打包为exe
echo ========================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到Python，请先安装Python
    pause
    exit /b 1
)

echo ✓ Python环境检查通过
echo.

echo 正在安装依赖包...
pip install pyinstaller pyautogui pillow pywin32 pyperclip

echo.
echo 正在打包程序...
pyinstaller --onefile --windowed --name="继续教育视频学习工具" video_study_gui.py

if exist "dist\继续教育视频学习工具.exe" (
    echo.
    echo ✓ 打包成功！
    echo exe文件位置: dist\继续教育视频学习工具.exe
    echo.
    echo 清理构建文件...
    if exist "build" rmdir /s /q "build"
    if exist "__pycache__" rmdir /s /q "__pycache__"
    if exist "video_study_gui.spec" del "video_study_gui.spec"
    echo ✓ 清理完成
) else (
    echo ❌ 打包失败
)

echo.
pause
